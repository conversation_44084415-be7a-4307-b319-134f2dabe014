import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';

// Dio实例Provider
final dioProvider = Provider<Dio>((ref) {
  final dio = Dio();
  dio.options.baseUrl = 'https://example.com'; // 替换为实际API地址
  dio.options.connectTimeout = const Duration(seconds: 10);
  dio.options.receiveTimeout = const Duration(seconds: 10);

  // 添加请求/响应拦截器
  // dio.interceptors.add(LogInterceptor(requestBody: true, responseBody: true));

  return dio;
});
